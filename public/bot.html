<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cute CSS Bot Animation</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom font */
        body {
            font-family: 'Inter', sans-serif;
        }

        /* The main container for the bot, used for the floating animation */
        .bot-container {
            position: relative;
            animation: float 6s ease-in-out infinite;
        }

        /* The bot's main body */
        .bot {
            width: 220px;
            height: 260px;
            position: relative;
            transition: all 0.3s ease;
        }

        /* The bot's head - now a softer, more rounded square */
        .head {
            width: 190px;
            height: 150px;
            background: linear-gradient(180deg, #fdfdff, #e8eaf0);
            border-radius: 55px; /* Cuter, rounder head */
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08), inset 0 2px 4px rgba(255, 255, 255, 0.9);
            border: 2px solid #fff;
        }
        
        /* Little antenna on top - smaller and rounder */
        .head::before {
            content: '';
            width: 20px;
            height: 10px;
            background: #e8eaf0;
            border-radius: 6px 6px 0 0;
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            border: 1px solid #fff;
        }

        /* The bot's face/screen */
        .face {
            width: 150px;
            height: 90px;
            background: #0f121a;
            border-radius: 30px; /* Softer screen corners */
            position: absolute;
            top: 25px;
            left: 50%;
            transform: translateX(-50%);
            overflow: hidden;
            box-shadow: inset 0 0 15px #1a2a4a;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* The glowing effect on the face */
        .face::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 30px;
            box-shadow: 0 0 25px 5px rgba(110, 180, 255, 0.5);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        /* Icon for correct/wrong state */
        .face::before {
            content: '';
            position: absolute;
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        }

        .bot.happy .face::after, .bot.talking .face::after, .bot.wave .face::after {
            opacity: 1;
        }

        /* Container for the eyes and mouth */
        .features {
             position: relative;
             width: 100%;
             height: 100%;
             transition: opacity 0.3s ease;
        }

        /* Container for the eyes */
        .eyes {
            position: absolute;
            top: 30px;
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 35px;
        }

        /* Individual eye styling - now simple circles */
        .eye {
            width: 22px;
            height: 22px;
            background: #6eb4ff;
            border-radius: 50%;
            box-shadow: 0 0 12px #6eb4ff;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            animation: blink 4s infinite;
        }

        /* Eye pupil for more realistic look */
        .eye::after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: #1a2a4a;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.2s ease;
        }

        /* The bot's body - rounder and softer */
        .body-bot {
            width: 160px;
            height: 130px;
            background: linear-gradient(180deg, #f5f6fa, #dce0e6);
            border-radius: 40% 40% 70% 70%;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), inset 0 4px 6px rgba(255, 255, 255, 1);
            border: 2px solid #fff;
            animation: breathing 4s ease-in-out infinite;
        }
        
        /* Decorative line on the body */
        .body-bot::before {
            content: '';
            width: 50px;
            height: 3px;
            background: #c0c6d0;
            position: absolute;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        /* Arms styling - shorter and rounder "nubs" */
        .arm {
            width: 35px;
            height: 80px;
            background: linear-gradient(180deg, #f5f6fa, #dce0e6);
            border-radius: 18px;
            position: absolute;
            top: 160px;
            z-index: -1;
            box-shadow: 0 4px 10px rgba(0,0,0,0.08);
            border: 2px solid #fff;
            transform-origin: top center;
        }

        .arm.left {
            left: 2px;
            animation: swing-left 3s ease-in-out infinite;
        }

        .arm.right {
            right: 2px;
            animation: swing-right 3s ease-in-out infinite 0.5s;
        }

        /* Mouth styling - simple line for idle */
        .mouth {
            width: 35px;
            height: 4px;
            background: #6eb4ff;
            border-radius: 2px;
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            box-shadow: 0 0 8px #6eb4ff;
        }

        /* --- STATE MODIFIERS --- */

        /* Happy State - simple curved mouth */
        .bot.happy .eye, .bot.correct .eye, .bot.wave .eye {
            transform: scale(1.1);
            animation: happy-eyes 2s ease-in-out infinite;
        }
        .bot.happy .mouth, .bot.correct .mouth, .bot.wave .mouth {
            width: 40px;
            height: 10px;
            background: transparent;
            border-radius: 0 0 20px 20px;
            border: 4px solid #6eb4ff;
            border-top: 0;
            box-shadow: 0 0 10px #6eb4ff;
        }

        /* Sad State - simple downturned mouth */
        .bot.sad .eye, .bot.wrong .eye {
            background: #5a8fcc;
            box-shadow: 0 0 8px #5a8fcc;
            height: 20px;
        }
        .bot.sad .mouth, .bot.wrong .mouth {
            width: 40px;
            height: 20px;
            background: transparent;
            border-radius: 20px 20px 0 0;
            border: 4px solid #5a8fcc;
            border-bottom: 0;
            box-shadow: 0 0 10px #5a8fcc;
        }

        /* Talking State */
        .bot.talking .mouth {
            animation: talk-realistic 0.3s infinite;
        }

        .bot.talking .eye::after {
            animation: eye-movement 2s infinite;
        }

        /* Correct State */
        .bot.correct .face::after {
             box-shadow: 0 0 35px 8px rgba(34, 197, 94, 0.7);
             opacity: 1;
        }
        .bot.correct .face .features, .bot.wrong .face .features {
            opacity: 0;
        }
        .bot.correct .face::before {
            width: 25px;
            height: 50px;
            border-bottom: 12px solid #22c55e;
            border-right: 12px solid #22c55e;
            transform: rotate(45deg) scale(1);
            opacity: 1;
        }

        /* Wrong State */
        .bot.wrong .face::after {
             box-shadow: 0 0 35px 8px rgba(239, 68, 68, 0.7);
             opacity: 1;
        }
        .bot.wrong .face::before {
            content: 'X';
            font-size: 70px;
            font-weight: bold;
            color: #ef4444;
            transform: scale(1);
            opacity: 1;
        }
        
        /* Wave State */
        .bot.wave .arm.right {
            animation: wave-arm-realistic 2s ease-in-out;
        }

        .bot.wave .eye::after {
            animation: eye-follow 2s ease-in-out;
        }


        /* --- KEYFRAME ANIMATIONS --- */

        @keyframes float {
            0% { transform: translateY(0px); }
            25% { transform: translateY(-8px); }
            50% { transform: translateY(-12px); }
            75% { transform: translateY(-8px); }
            100% { transform: translateY(0px); }
        }

        @keyframes blink {
            0%, 90%, 100% { transform: scaleY(1); }
            95% { transform: scaleY(0.1); }
        }

        @keyframes swing-left {
            0% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            50% { transform: rotate(12deg); }
            75% { transform: rotate(5deg); }
            100% { transform: rotate(0deg); }
        }

        @keyframes swing-right {
            0% { transform: rotate(0deg); }
            25% { transform: rotate(-5deg); }
            50% { transform: rotate(-12deg); }
            75% { transform: rotate(-5deg); }
            100% { transform: rotate(0deg); }
        }

        @keyframes talk-realistic {
            0% { transform: translateX(-50%) scaleY(0.8) scaleX(1); }
            20% { transform: translateX(-50%) scaleY(1.8) scaleX(1.1); }
            40% { transform: translateX(-50%) scaleY(0.6) scaleX(0.9); }
            60% { transform: translateX(-50%) scaleY(2.0) scaleX(1.2); }
            80% { transform: translateX(-50%) scaleY(1.2) scaleX(1); }
            100% { transform: translateX(-50%) scaleY(0.8) scaleX(1); }
        }

        @keyframes eye-movement {
            0%, 100% { transform: translate(-50%, -50%); }
            25% { transform: translate(-40%, -50%); }
            50% { transform: translate(-60%, -50%); }
            75% { transform: translate(-50%, -40%); }
        }

        @keyframes wave-arm-realistic {
            0% { transform: rotate(-10deg); }
            10% { transform: rotate(-45deg); }
            20% { transform: rotate(-15deg); }
            30% { transform: rotate(-60deg); }
            40% { transform: rotate(-20deg); }
            50% { transform: rotate(-70deg); }
            60% { transform: rotate(-25deg); }
            70% { transform: rotate(-55deg); }
            80% { transform: rotate(-15deg); }
            90% { transform: rotate(-30deg); }
            100% { transform: rotate(-10deg); }
        }

        @keyframes eye-follow {
            0%, 100% { transform: translate(-50%, -50%); }
            25% { transform: translate(-30%, -50%); }
            50% { transform: translate(-20%, -40%); }
            75% { transform: translate(-40%, -45%); }
        }

        @keyframes breathing {
            0%, 100% { transform: translateX(-50%) scale(1); }
            50% { transform: translateX(-50%) scale(1.02); }
        }

        @keyframes happy-eyes {
            0%, 100% { transform: scale(1.1); }
            50% { transform: scale(1.2); }
        }

    </style>
</head>
<body class="bg-gray-100 flex flex-col items-center justify-center min-h-screen p-4">

    <div class="bot-container mb-12">
        <!-- The Bot -->
        <div id="bot" class="bot idle">
            <div class="head">
                <div class="face">
                    <div class="features">
                        <div class="eyes">
                            <div class="eye left"></div>
                            <div class="eye right"></div>
                        </div>
                        <div class="mouth"></div>
                    </div>
                </div>
            </div>
            <div class="body-bot"></div>
            <div class="arm left"></div>
            <div class="arm right"></div>
        </div>
    </div>

    <!-- Controls -->
    <div class="flex flex-wrap justify-center gap-3">
        <button data-state="idle" class="state-btn bg-white text-gray-700 font-semibold py-2 px-5 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all">Idle</button>
        <button data-state="happy" class="state-btn bg-white text-gray-700 font-semibold py-2 px-5 border border-gray-300 rounded-lg shadow-sm hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all">Happy</button>
        <button data-state="sad" class="state-btn bg-white text-gray-700 font-semibold py-2 px-5 border border-gray-300 rounded-lg shadow-sm hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all">Sad</button>
        <button data-state="talking" class="state-btn bg-white text-gray-700 font-semibold py-2 px-5 border border-gray-300 rounded-lg shadow-sm hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all">Talking</button>
        <button data-state="wave" class="state-btn bg-white text-gray-700 font-semibold py-2 px-5 border border-gray-300 rounded-lg shadow-sm hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all">Wave</button>
        <button data-state="correct" class="state-btn bg-white text-gray-700 font-semibold py-2 px-5 border border-gray-300 rounded-lg shadow-sm hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-600 transition-all">Correct</button>
        <button data-state="wrong" class="state-btn bg-white text-gray-700 font-semibold py-2 px-5 border border-gray-300 rounded-lg shadow-sm hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-600 transition-all">Wrong</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const bot = document.getElementById('bot');
            const stateButtons = document.querySelectorAll('.state-btn');
            let currentTimeout = null;

            // Function to set the bot's state
            function setBotState(state) {
                // Clear any existing timeout
                if (currentTimeout) {
                    clearTimeout(currentTimeout);
                    currentTimeout = null;
                }

                // Remove all possible state classes
                bot.classList.remove('idle', 'happy', 'sad', 'talking', 'correct', 'wrong', 'wave');

                // Add the new state class with a slight delay for smooth transition
                setTimeout(() => {
                    if (state) {
                        bot.classList.add(state);
                    }
                }, 50);

                // Update button states
                stateButtons.forEach(btn => {
                    btn.classList.remove('bg-blue-100', 'border-blue-300');
                    if (btn.dataset.state === state) {
                        btn.classList.add('bg-blue-100', 'border-blue-300');
                    }
                });
            }

            // Add click event listeners to all state buttons
            stateButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const state = button.dataset.state;
                    setBotState(state);

                    // For correct/wrong/wave, revert to idle after a delay
                    if (state === 'correct' || state === 'wrong' || state === 'wave') {
                        currentTimeout = setTimeout(() => {
                            // Only revert if the state hasn't been changed by the user again
                            if(bot.classList.contains(state)) {
                                setBotState('idle');
                            }
                        }, state === 'wave' ? 2500 : 2000); // Wave lasts a bit longer
                    }
                });
            });

            // Add hover effects for more interactivity
            bot.addEventListener('mouseenter', () => {
                if (bot.classList.contains('idle')) {
                    setBotState('happy');
                    currentTimeout = setTimeout(() => {
                        if(bot.classList.contains('happy')) {
                            setBotState('idle');
                        }
                    }, 1500);
                }
            });

            // Random idle animations
            function randomIdleAnimation() {
                if (bot.classList.contains('idle')) {
                    const randomActions = ['wave', 'happy'];
                    const randomAction = randomActions[Math.floor(Math.random() * randomActions.length)];

                    setBotState(randomAction);
                    currentTimeout = setTimeout(() => {
                        if(bot.classList.contains(randomAction)) {
                            setBotState('idle');
                        }
                    }, 1500);
                }
            }

            // Trigger random animations every 10-15 seconds
            setInterval(randomIdleAnimation, Math.random() * 5000 + 10000);

            // Set the initial state to 'idle'
            setBotState('idle');
        });
    </script>

</body>
</html>
